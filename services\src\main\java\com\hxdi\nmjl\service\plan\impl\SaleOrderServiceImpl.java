package com.hxdi.nmjl.service.plan.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.nmjl.condition.plan.SaleCondition;
import com.hxdi.nmjl.domain.bigscreen.OrderMonthRecord;
import com.hxdi.nmjl.domain.inout.InoutDetail;
import com.hxdi.nmjl.domain.inout.InoutItem;
import com.hxdi.nmjl.domain.inout.InoutTask;
import com.hxdi.nmjl.domain.plan.SaleOrder;
import com.hxdi.nmjl.domain.plan.SaleOrderItem;
import com.hxdi.nmjl.feign.SystemNumberRuleClient;
import com.hxdi.nmjl.mapper.plan.SaleOrderMapper;
import com.hxdi.nmjl.service.bigscreen.OrderMonthRecordService;
import com.hxdi.nmjl.service.inout.InoutDetailService;
import com.hxdi.nmjl.service.inout.InoutTaskService;
import com.hxdi.nmjl.service.plan.ContractInfoService;
import com.hxdi.nmjl.service.plan.SaleOrderItemService;
import com.hxdi.nmjl.service.plan.SaleOrderService;
import com.hxdi.nmjl.utils.DataConvertUtil;
import com.hxdi.nmjl.vo.plan.SaleOrderStatVO;
import com.hxdi.nmjl.vo.plan.SaleOrderStatVOItem;
import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <销售订单服务实现>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/25 14:20
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class SaleOrderServiceImpl extends BaseServiceImpl<SaleOrderMapper, SaleOrder> implements SaleOrderService {

    @Resource
    private SystemNumberRuleClient systemNumberRuleClient;

    @Resource
    private InoutTaskService inoutTaskService;

    @Resource
    private InoutDetailService inoutDetailService;

    @Resource
    private SaleOrderItemService saleOrderItemService;

    @Resource
    private ContractInfoService contractInfoService;

    @Resource
    private OrderMonthRecordService orderMonthRecordService;

    @Override
    public SaleOrder getDetail(String orderId) {
        SaleOrder saleOrder = baseMapper.selectById(orderId);
        saleOrder.setDetailList(saleOrderItemService.getList(orderId));
        return saleOrder;
    }

    @Override
    public SaleOrderStatVO getOrderStat(String storeId, String year) {
        // 参数校验
        if (CommonUtils.isEmpty(storeId) || CommonUtils.isEmpty(year)) {
            throw new BaseException("库点ID和统计年份不能为空");
        }

        SaleOrderStatVO result = new SaleOrderStatVO();

        // 查询订单总数
        Integer orderCount = baseMapper.selectOrderCount(storeId, year);
        result.setOrderCount(orderCount != null ? orderCount : 0);

        // 查询订单总金额
        BigDecimal orderAmount = baseMapper.selectOrderAmount(storeId, year);
        result.setOrderAmount(orderAmount != null ? orderAmount : BigDecimal.ZERO);

        // 查询订单最多的来源
        String popularSource = baseMapper.selectPopularSource(storeId, year);
        result.setPopularSource(popularSource);

        // 查询订单最多的品类
        String popularCatalog = baseMapper.selectPopularCatalog(storeId, year);
        result.setPopularCatalog(popularCatalog);

        // 查询按月统计的订单数量
        List<SaleOrderStatVOItem> countStat = baseMapper.selectMonthlyOrderCount(storeId, year);
        result.setCountStat(countStat != null ? countStat : new ArrayList<>());

        // 查询按月统计的订单金额
        List<SaleOrderStatVOItem> amountStat = baseMapper.selectMonthlyOrderAmount(storeId, year);
        result.setAmountStat(amountStat != null ? amountStat : new ArrayList<>());

        return result;
    }

    @Override
    public Page<SaleOrder> pages(SaleCondition condition) {
        Page<SaleOrder> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);
        // page.getRecords().forEach(item -> item.setDetailList(saleDetailService.getList(item.getId())));
        return page;
    }

    @Override
    public Page<SaleOrder> PagesV1(SaleCondition condition) {
        Page<SaleOrder> page = condition.newPage();
        baseMapper.selectPageV1(page, condition);

        List<SaleOrder> orders = page.getRecords();
        if (CommonUtils.isEmpty(orders)) {
            return page;
        }

        //查询所有相关订单的出库任务
        List<String> orderIds = orders.stream().map(SaleOrder::getId).collect(Collectors.toList());
        List<InoutTask> tasks = inoutTaskService.list(Wrappers.<InoutTask>lambdaQuery()
                .in(InoutTask::getOrderId, orderIds)
                .eq(InoutTask::getEnabled, 1));

        if (CommonUtils.isEmpty(tasks)) {
            // 如果没有任务，为所有订单设置空子列表并返回
            orders.forEach(order -> order.setChildren(Collections.emptyList()));
            return page;
        }

        //查询所有相关任务的出库明细
        List<String> taskCodes = tasks.stream().map(InoutTask::getTaskCode).collect(Collectors.toList());
        List<InoutDetail> details = inoutDetailService.list(Wrappers.<InoutDetail>lambdaQuery()
                .in(InoutDetail::getTaskCode, taskCodes)
                .eq(InoutDetail::getEnabled, 1));

        // 将明细按 taskCode 分组
        Map<String, List<InoutDetail>> detailsByTaskCode = details.stream()
                .collect(Collectors.groupingBy(InoutDetail::getTaskCode));

        // 将任务与其明细关联
        tasks.forEach(task -> task.setChildren(detailsByTaskCode.getOrDefault(task.getTaskCode(), Collections.emptyList())));

        // 将任务按 orderId 分组
        Map<String, List<InoutTask>> tasksByOrderId = tasks.stream()
                .collect(Collectors.groupingBy(InoutTask::getOrderId));

        // 将任务列表设置到对应的订单中
        orders.forEach(order -> order.setChildren(tasksByOrderId.getOrDefault(order.getId(), Collections.emptyList())));

        return page;
    }

    @Override
    public List<SaleOrder> lists(SaleCondition condition) {
        return baseMapper.selectListV1(condition);
    }

    @Override
    public List<SaleOrder> listsV1(SaleCondition condition) {
        List<SaleOrder> orderList = baseMapper.selectListV1(condition);
        if (orderList.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> idList = orderList.stream().map(SaleOrder::getId).collect(Collectors.toList());
        List<SaleOrderItem> itemList = saleOrderItemService.getList(idList);
        if (!itemList.isEmpty()) {
            Map<String, List<SaleOrderItem>> itemMap = itemList.stream().collect(Collectors.groupingBy(SaleOrderItem::getOrderId));
            orderList.forEach(item -> item.setDetailList(itemMap.getOrDefault(item.getId(), Collections.emptyList())));
        }
        return orderList;
    }

    public void createV1(SaleOrder saleOrder) {

        //生成订单编码
        BusinessCodeParams params = new BusinessCodeParams();
        params.setCode("SALE_ORDER_CODE");
        params.setDt(DataType.STRING);
        BusinessCode businessCode = systemNumberRuleClient.getNumber(params).getData();
        saleOrder.setOrderCode((String) businessCode.getValue());

        saleOrder.setState(1);
        //将数据权限设置为库点
        String dataHierarchyId = saleOrder.getStoreId();
        saleOrder.setDataHierarchyId(dataHierarchyId);

        this.save(saleOrder);
        saleOrder.getDetailList().forEach(detail -> {
            detail.setOrderId(saleOrder.getId());
            detail.setContractId(saleOrder.getContractId());
            detail.setDataHierarchyId(dataHierarchyId);
        });
        saleOrderItemService.saveBatch(saleOrder.getDetailList());
    }

    public void updateV1(SaleOrder saleOrder) {
        this.updateById(saleOrder);
        if (saleOrder.getDetailList() != null) {
            saleOrderItemService.removeV1(saleOrder.getId());
            saleOrder.getDetailList().forEach(detail -> {
                detail.setOrderId(saleOrder.getId());
                detail.setContractId(saleOrder.getContractId());
            });
            saleOrderItemService.saveBatch(saleOrder.getDetailList());
        }
    }

    @Override
    public void removeV1(String orderId) {
        //逻辑删除
        SaleOrder saleOrder = new SaleOrder();
        saleOrder.setId(orderId);
        saleOrder.setEnabled(0);
        this.updateById(saleOrder);
    }

    @Override
    public void submitV1(String orderId) {
        SaleOrder saleOrder = this.getById(orderId);
        if (saleOrder.getState() != 1) {
            throw new BaseException("当前订单已确认，无法再次提交！");
        }
        saleOrder.setApproveStatus(0);

        if (saleOrder.getApproveStatus() != null && saleOrder.getApproveStatus() == 2) {
            //清除审核信息
            saleOrder.setApprover("");
            saleOrder.setApproveOpinion("");
        }
        this.updateById(saleOrder);
    }

    @Override
    public void approve(String id, String approveOpinion) {
        changeApproveStatus(id, 1, approveOpinion);
    }

    @Override
    public void reject(String id, String approveOpinion) {
        changeApproveStatus(id, 2, approveOpinion);
    }

    public void updateState(String id, Integer state) {
        SaleOrder saleOrder = this.getById(id);
        if (saleOrder.getState().equals(state)) {
            throw new BaseException("当前订单状态与目标状态一致，无需更新！");
        }
        saleOrder.setState(state);
        this.updateById(saleOrder);

        //已完成的订单，更新合同、标的
        if (state == 6) {
            List<SaleOrderItem> itemList = saleOrderItemService.getList(id);
            contractInfoService.updateProcessV1(itemList);
        }
    }

    @Override
    public void updateSettlementStatus(String orderId) {
        if (CommonUtils.isBlank(orderId)) {
            return;
        }

        LambdaUpdateWrapper<SaleOrder> updateWrapper = Wrappers.<SaleOrder>lambdaUpdate()
                .in(SaleOrder::getId, DataConvertUtil.ObjToList(orderId))
                .set(SaleOrder::getSettlementStatus, 1);

        this.update(updateWrapper);
    }

    @Override
    public void updateProcess(String orderId, String catalogId, String grade, BigDecimal changeQty) {
        SaleOrder order = this.getById(orderId);
        if (order == null) {
            throw new BaseException("订单不存在");
        }
        SaleOrderItem item = saleOrderItemService.listByCatalogIdAndGrade(orderId, catalogId, grade);
        if (item == null) {
            throw new BaseException("订单明细不存在");
        }
        //更新完成数量
        BigDecimal currentQty = item.getCompletedQty() != null ? item.getCompletedQty() : BigDecimal.ZERO;
        item.setCompletedQty(currentQty.add(changeQty));

        //查询当前是否所有的品种都已完成
        List<SaleOrderItem> itemList = saleOrderItemService.getList(orderId);
        boolean allCompleted = itemList.stream().allMatch(detail -> detail.getCompletedQty().compareTo(detail.getOrderQty()) >= 0);
        if (allCompleted) {
            //更新订单状态为已完成
            this.updateState(orderId, 6);
        }

        saleOrderItemService.updateById(item);
    }

    @Override
    public void generateOrderStatistics(List<InoutDetail> inoutDetailList) {
        // 优化版本：批量处理销售订单统计数据生成

        if (inoutDetailList == null || inoutDetailList.isEmpty()) {
            log.warn("出入库明细数据为空，跳过销售订单统计生成");
            return;
        }

        // 先根据订单编号分组
        Map<String, List<InoutDetail>> orderMap = inoutDetailList.stream()
                .filter(detail -> detail.getOrderCode() != null && !detail.getOrderCode().trim().isEmpty())
                .collect(Collectors.groupingBy(InoutDetail::getOrderCode));

        if (orderMap.isEmpty()) {
            log.warn("没有有效的订单编号，跳过销售订单统计生成");
            return;
        }

        // 批量查询所有订单信息
        Map<String, SaleOrder> orderCache = new HashMap<>();
        List<String> orderCodes = new ArrayList<>(orderMap.keySet());

        List<SaleOrder> orderList = this.list(Wrappers.<SaleOrder>lambdaQuery()
                .in(SaleOrder::getOrderCode, orderCodes)
                .eq(SaleOrder::getEnabled, 1));

        if (!orderList.isEmpty()) {
            // 批量查询订单明细
            List<String> orderIds = orderList.stream().map(SaleOrder::getId).collect(Collectors.toList());
            List<SaleOrderItem> allItems = saleOrderItemService.getList(orderIds);

            // 按订单ID分组明细
            Map<String, List<SaleOrderItem>> itemMap = allItems.stream()
                    .collect(Collectors.groupingBy(SaleOrderItem::getOrderId));

            // 设置明细并构建缓存
            orderList.forEach(order -> {
                order.setDetailList(itemMap.getOrDefault(order.getId(), Collections.emptyList()));
                orderCache.put(order.getOrderCode(), order);
            });
        }

        // 准备批量统计记录
        List<OrderMonthRecord> recordList = new ArrayList<>();

        orderMap.forEach((orderCode, detailList) -> {
            try {
                SaleOrder saleOrder = orderCache.get(orderCode);
                if (saleOrder == null) {
                    log.warn("未找到订单编号为 {} 的销售订单", orderCode);
                    return;
                }

                // 从出入库明细中统计完成数量和获取品种信息
                BigDecimal totalCompletedQty = BigDecimal.ZERO;
                String catalogId = null;
                String grade = null;
                String areaCode = null;
                String reserveLevel = null;

                for (InoutDetail detail : detailList) {
                    if (areaCode == null) {
                        areaCode = detail.getAreaCode();
                    }
                    if (reserveLevel == null) {
                        reserveLevel = String.valueOf(detail.getReserveLevel());
                    }
                    if (detail.getDetailList() != null && !detail.getDetailList().isEmpty()) {
                        for (InoutItem item : detail.getDetailList()) {
                            if (catalogId == null) {
                                catalogId = item.getCatalogId();
                                grade = item.getGrade();
                            }
                            if (item.getQty() != null) {
                                totalCompletedQty = totalCompletedQty.add(item.getQty());
                            }
                        }
                    }
                }

                // 根据品种信息匹配订单明细
                if (catalogId != null && grade != null && reserveLevel != null && saleOrder.getDetailList() != null) {
                    SaleOrderItem matchedItem = null;
                    for (SaleOrderItem item : saleOrder.getDetailList()) {
                        if (catalogId.equals(item.getCatalogId()) && grade.equals(item.getGrade()) && reserveLevel.equals(item.getReserveLevel())) {
                            matchedItem = item;
                            break;
                        }
                    }

                    if (matchedItem != null) {
                        // 构建统计记录
                        OrderMonthRecord record = orderMonthRecordService.buildSaleOrderRecord(saleOrder, matchedItem, totalCompletedQty, areaCode);
                        recordList.add(record);

                        log.debug("准备生成销售订单 {} 的统计数据，完成数量: {}", orderCode, totalCompletedQty);
                    } else {
                        log.warn("未找到匹配的销售订单明细，订单: {}, catalogId: {}, grade: {}",
                                orderCode, catalogId, grade);
                    }
                }

            } catch (Exception e) {
                log.error("处理销售订单 {} 统计数据失败: {}", orderCode, e.getMessage(), e);
            }
        });

        // 批量保存统计记录
        if (!recordList.isEmpty()) {
            orderMonthRecordService.generateRecordsBatch(recordList);
            log.info("批量生成销售订单统计数据完成，共处理 {} 个订单，生成 {} 条统计记录", orderMap.size(), recordList.size());
        } else {
            log.warn("没有生成任何销售订单统计记录");
        }
    }


    /**
     * 更新审核状态
     *
     * @param id
     * @param approveStatus
     * @param approveOpinion
     */
    private void changeApproveStatus(String id, int approveStatus, String approveOpinion) {
        SaleOrder saleOrder = baseMapper.selectById(id);
        if (saleOrder == null || saleOrder.getEnabled() == 0) {
            throw new BaseException("订单信息不存在！");
        }

        // 校验状态：已审核的不能重复审核
        if (approveStatus != 0 && saleOrder.getApproveStatus() != null && saleOrder.getApproveStatus() != 0) {
            throw new BaseException("该订单信息已审核，无法重复操作！");
        }

        saleOrder.setApproveStatus(approveStatus);
        saleOrder.setApprover(SecurityHelper.obtainUser().getNickName());
        saleOrder.setApproveTime(new Date());
        saleOrder.setApproveOpinion(approveOpinion);

        if (approveStatus == 1) {
            saleOrder.setState(2);
        }


        baseMapper.updateById(saleOrder);
    }
}
