package com.hxdi.nmjl.vo.plan;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @program: nmjl-service
 * @description: 销售订单按月统计明细VO
 * @author: 王贝强
 * @create: 2025-08-29 16:13
 */
@Getter
@Setter
public class SaleOrderStatVOItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 统计月份
     */
    private String statMonth;

    /**
     * 数据类型：1-订单数量，2-订单金额
     */
    private String type;

    /**
     * 数量或金额
     */
    private String amount;
}
