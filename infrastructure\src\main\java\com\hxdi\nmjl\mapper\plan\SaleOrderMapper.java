package com.hxdi.nmjl.mapper.plan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.nmjl.condition.plan.SaleCondition;
import com.hxdi.nmjl.domain.plan.SaleOrder;
import com.hxdi.nmjl.vo.plan.SaleOrderStatVOItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface SaleOrderMapper extends SuperMapper<SaleOrder> {

    @DataPermission
    Page<SaleOrder> selectPageV1(Page<SaleOrder> page,@Param("condition") SaleCondition condition);

    List<SaleOrder> selectListV1(@Param("condition") SaleCondition condition);

    /**
     * 查询订单总数
     * @param storeId 库点ID
     * @param year 统计年份
     * @return 订单总数
     */
    Integer selectOrderCount(@Param("storeId") String storeId, @Param("year") String year);

    /**
     * 查询订单总金额
     * @param storeId 库点ID
     * @param year 统计年份
     * @return 订单总金额
     */
    BigDecimal selectOrderAmount(@Param("storeId") String storeId, @Param("year") String year);

    /**
     * 查询订单最多的来源
     * @param storeId 库点ID
     * @param year 统计年份
     * @return 订单最多的来源
     */
    String selectPopularSource(@Param("storeId") String storeId, @Param("year") String year);

    /**
     * 查询订单最多的品类
     * @param storeId 库点ID
     * @param year 统计年份
     * @return 订单最多的品类
     */
    String selectPopularCatalog(@Param("storeId") String storeId, @Param("year") String year);

    /**
     * 查询按月统计的订单数量
     * @param storeId 库点ID
     * @param year 统计年份
     * @return 按月统计的订单数量
     */
    List<SaleOrderStatVOItem> selectMonthlyOrderCount(@Param("storeId") String storeId, @Param("year") String year);

    /**
     * 查询按月统计的订单金额
     * @param storeId 库点ID
     * @param year 统计年份
     * @return 按月统计的订单金额
     */
    List<SaleOrderStatVOItem> selectMonthlyOrderAmount(@Param("storeId") String storeId, @Param("year") String year);
}