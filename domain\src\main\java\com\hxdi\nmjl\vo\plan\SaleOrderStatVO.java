package com.hxdi.nmjl.vo.plan;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @program: nmjl-service
 * @description: 销售订单统计
 * @author: 王贝强
 * @create: 2025-08-29 15:55
 */
@Setter
@Getter
@ApiModel(description = "销售订单统计VO")
public class SaleOrderStatVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单总数
     */
    @ApiModelProperty(value = "订单总数")
    private Integer orderCount;

    /**
     * 订单总金额
     */
    @ApiModelProperty(value = "订单总金额")
    private BigDecimal orderAmount;

    /**
     * 订单最多的来源
     */
    @ApiModelProperty(value = "订单最多的来源")
    private String popularSource;

    /**
     * 订单最多的品类
     */
    @ApiModelProperty(value = "订单最多的品类")
    private String popularCatalog;


    /**
     * 订单金额按月统计
     */
    private List<SaleOrderStatVOItem> amountStat;

    /**
     * 订单数量按月统计
     */
    private List<SaleOrderStatVOItem> countStat;


}
